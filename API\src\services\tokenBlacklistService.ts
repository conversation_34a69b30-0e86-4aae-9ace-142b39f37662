import { CacheService } from "../config/cache";
import jwt from "jsonwebtoken";

/**
 * Token Blacklist Service
 * 
 * Manages blacklisted JWT tokens to implement proper logout functionality.
 * Uses the existing cache system for performance and automatic cleanup.
 */
export class TokenBlacklistService {
  private static readonly BLACKLIST_PREFIX = "blacklist:token:";
  
  /**
   * Add a token to the blacklist
   * @param token - JWT token to blacklist
   * @returns Promise<boolean> - Success status
   */
  static async blacklistToken(token: string): Promise<boolean> {
    try {
      // Decode token to get expiration time (without verification since we're blacklisting it)
      const decoded = jwt.decode(token) as any;
      
      if (!decoded || !decoded.exp) {
        // If we can't decode or no expiration, blacklist for default duration
        const defaultTTL = 24 * 60 * 60; // 24 hours in seconds
        return CacheService.set(
          this.getBlacklistKey(token),
          { blacklistedAt: new Date().toISOString() },
          defaultTTL
        );
      }
      
      // Calculate TTL based on token expiration
      const now = Math.floor(Date.now() / 1000);
      const ttl = Math.max(0, decoded.exp - now);
      
      if (ttl <= 0) {
        // Token already expired, no need to blacklist
        return true;
      }
      
      // Blacklist token until its natural expiration
      return CacheService.set(
        this.getBlacklistKey(token),
        { 
          blacklistedAt: new Date().toISOString(),
          originalExp: decoded.exp,
          userId: decoded.userId 
        },
        ttl
      );
      
    } catch (error) {
      console.error("Error blacklisting token:", error);
      return false;
    }
  }
  
  /**
   * Check if a token is blacklisted
   * @param token - JWT token to check
   * @returns Promise<boolean> - True if blacklisted
   */
  static async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistEntry = CacheService.get(this.getBlacklistKey(token));
      return blacklistEntry !== null;
    } catch (error) {
      console.error("Error checking token blacklist:", error);
      // In case of error, assume token is not blacklisted to avoid blocking valid requests
      return false;
    }
  }
  
  /**
   * Remove a token from blacklist (mainly for testing)
   * @param token - JWT token to remove from blacklist
   * @returns Promise<boolean> - Success status
   */
  static async removeFromBlacklist(token: string): Promise<boolean> {
    try {
      return CacheService.del(this.getBlacklistKey(token));
    } catch (error) {
      console.error("Error removing token from blacklist:", error);
      return false;
    }
  }
  
  /**
   * Blacklist all tokens for a specific user (useful for security incidents)
   * @param userId - User ID to blacklist all tokens for
   * @returns Promise<boolean> - Success status
   */
  static async blacklistAllUserTokens(userId: string): Promise<boolean> {
    try {
      const userBlacklistKey = `${this.BLACKLIST_PREFIX}user:${userId}`;
      const ttl = 24 * 60 * 60; // 24 hours
      
      return CacheService.set(
        userBlacklistKey,
        { 
          blacklistedAt: new Date().toISOString(),
          reason: "All user tokens blacklisted"
        },
        ttl
      );
    } catch (error) {
      console.error("Error blacklisting all user tokens:", error);
      return false;
    }
  }
  
  /**
   * Check if all tokens for a user are blacklisted
   * @param userId - User ID to check
   * @returns Promise<boolean> - True if all user tokens are blacklisted
   */
  static async areAllUserTokensBlacklisted(userId: string): Promise<boolean> {
    try {
      const userBlacklistKey = `${this.BLACKLIST_PREFIX}user:${userId}`;
      const blacklistEntry = CacheService.get(userBlacklistKey);
      return blacklistEntry !== null;
    } catch (error) {
      console.error("Error checking user token blacklist:", error);
      return false;
    }
  }
  
  /**
   * Get cache statistics for blacklisted tokens
   * @returns Object with blacklist statistics
   */
  static getBlacklistStats(): { totalBlacklisted: number } {
    try {
      const keys = CacheService.getKeys();
      const blacklistedTokens = keys.filter(key => key.startsWith(this.BLACKLIST_PREFIX));
      
      return {
        totalBlacklisted: blacklistedTokens.length
      };
    } catch (error) {
      console.error("Error getting blacklist stats:", error);
      return { totalBlacklisted: 0 };
    }
  }
  
  /**
   * Clear all blacklisted tokens (mainly for testing/development)
   * @returns Promise<boolean> - Success status
   */
  static async clearAllBlacklisted(): Promise<boolean> {
    try {
      const keys = CacheService.getKeys();
      const blacklistedKeys = keys.filter(key => key.startsWith(this.BLACKLIST_PREFIX));
      
      let success = true;
      for (const key of blacklistedKeys) {
        if (!CacheService.del(key)) {
          success = false;
        }
      }
      
      return success;
    } catch (error) {
      console.error("Error clearing blacklisted tokens:", error);
      return false;
    }
  }
  
  /**
   * Generate cache key for blacklisted token
   * @param token - JWT token
   * @returns string - Cache key
   */
  private static getBlacklistKey(token: string): string {
    // Use a hash of the token to avoid storing the full token as key
    const crypto = require('crypto');
    const tokenHash = crypto.createHash('sha256').update(token).digest('hex');
    return `${this.BLACKLIST_PREFIX}${tokenHash}`;
  }
}
