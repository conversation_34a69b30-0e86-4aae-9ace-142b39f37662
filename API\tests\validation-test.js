/**
 * Validation Test for Profile Update
 * 
 * This test verifies that the validation middleware correctly validates
 * the expected field names for profile updates.
 */

const { body } = require('express-validator');

// Import the validation middleware
const { updateProfileValidation } = require('../src/middleware/validation');

/**
 * Test that validation middleware includes the correct field validators
 */
function testValidationFields() {
  console.log('Testing validation middleware field names...');
  
  const expectedFields = [
    'full_name',
    'mobile_number', 
    'current_location',
    'linkedin_url',
    'company',
    'job_title',
    'course_id',
    'batch_year',
    'privacy_settings',
    'privacy_settings.show_email',
    'privacy_settings.show_mobile',
    'privacy_settings.show_linkedin'
  ];
  
  // Check if validation array exists
  if (!Array.isArray(updateProfileValidation)) {
    console.log('❌ updateProfileValidation is not an array');
    return false;
  }
  
  console.log(`✅ updateProfileValidation is an array with ${updateProfileValidation.length} validators`);
  
  // Convert validation middleware to string to check for field names
  const validationString = updateProfileValidation.toString();
  
  let allFieldsFound = true;
  expectedFields.forEach(field => {
    if (validationString.includes(`"${field}"`)) {
      console.log(`✅ Found validation for field: ${field}`);
    } else {
      console.log(`❌ Missing validation for field: ${field}`);
      allFieldsFound = false;
    }
  });
  
  // Check that old field names are NOT present
  const oldFields = [
    'firstName',
    'lastName', 
    'mobile',
    'location',
    'linkedinUrl',
    'currentCompany',
    'currentPosition',
    'bio',
    'githubUrl',
    'portfolioUrl'
  ];
  
  let noOldFields = true;
  oldFields.forEach(field => {
    if (validationString.includes(`"${field}"`)) {
      console.log(`❌ Found old field name in validation: ${field}`);
      noOldFields = false;
    }
  });
  
  if (noOldFields) {
    console.log('✅ No old field names found in validation');
  }
  
  return allFieldsFound && noOldFields;
}

/**
 * Test the controller interface
 */
function testControllerInterface() {
  console.log('Testing controller interface...');
  
  try {
    const userController = require('../src/controllers/userController');
    
    if (typeof userController.updateProfile === 'function') {
      console.log('✅ updateProfile function exists in controller');
      
      // Check the function signature by converting to string
      const functionString = userController.updateProfile.toString();
      
      const expectedFields = [
        'full_name',
        'mobile_number',
        'current_location', 
        'linkedin_url',
        'company',
        'job_title',
        'course_id',
        'batch_year',
        'privacy_settings'
      ];
      
      let allFieldsInController = true;
      expectedFields.forEach(field => {
        if (functionString.includes(field)) {
          console.log(`✅ Controller handles field: ${field}`);
        } else {
          console.log(`❌ Controller missing field: ${field}`);
          allFieldsInController = false;
        }
      });
      
      return allFieldsInController;
    } else {
      console.log('❌ updateProfile function not found in controller');
      return false;
    }
  } catch (error) {
    console.error('❌ Error loading controller:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
function runTests() {
  console.log('Starting validation and controller tests...\n');
  
  const validationTest = testValidationFields();
  console.log('');
  
  const controllerTest = testControllerInterface();
  console.log('');
  
  if (validationTest && controllerTest) {
    console.log('✅ All tests passed! Profile update validation and controller are correctly configured.');
  } else {
    console.log('❌ Some tests failed. Please check the validation middleware and controller.');
  }
  
  console.log('\nTest Summary:');
  console.log(`Validation Test: ${validationTest ? 'PASS' : 'FAIL'}`);
  console.log(`Controller Test: ${controllerTest ? 'PASS' : 'FAIL'}`);
}

// Run the tests
runTests();
