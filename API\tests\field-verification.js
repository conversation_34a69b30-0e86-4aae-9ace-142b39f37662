/**
 * Field Verification Test
 *
 * This test verifies that the validation middleware and Swagger documentation
 * use the correct field names that match the database schema and controller.
 */

const fs = require("fs");
const path = require("path");

/**
 * Read and check validation middleware file
 */
function checkValidationMiddleware() {
  console.log("Checking validation middleware...");

  try {
    const validationPath = path.join(__dirname, "../src/middleware/validation.ts");
    const validationContent = fs.readFileSync(validationPath, "utf8");

    // Expected field names in validation
    const expectedFields = [
      "full_name",
      "mobile_number",
      "current_location",
      "linkedin_url",
      "company",
      "job_title",
      "course_id",
      "batch_year",
      "privacy_settings",
      "privacy_settings.show_email",
      "privacy_settings.show_mobile",
      "privacy_settings.show_linkedin",
    ];

    // Old field names that should NOT be present (checking for body() calls)
    const oldFields = [
      'body("firstName")',
      'body("lastName")',
      'body("mobile")', // Should be mobile_number
      'body("location")', // Should be current_location
      'body("linkedinUrl")',
      'body("currentCompany")',
      'body("currentPosition")',
      'body("bio")',
      'body("githubUrl")',
      'body("portfolioUrl")',
    ];

    let allExpectedFound = true;
    let noOldFieldsFound = true;

    // Check for expected fields
    expectedFields.forEach((field) => {
      if (validationContent.includes(`"${field}"`)) {
        console.log(`✅ Found expected field: ${field}`);
      } else {
        console.log(`❌ Missing expected field: ${field}`);
        allExpectedFound = false;
      }
    });

    // Check that old fields are not present
    oldFields.forEach((field) => {
      if (validationContent.includes(field)) {
        console.log(`❌ Found old field: ${field}`);
        noOldFieldsFound = false;
      }
    });

    if (noOldFieldsFound) {
      console.log("✅ No old field names found in validation");
    }

    return allExpectedFound && noOldFieldsFound;
  } catch (error) {
    console.error("❌ Error reading validation file:", error.message);
    return false;
  }
}

/**
 * Read and check Swagger documentation in routes
 */
function checkSwaggerDocumentation() {
  console.log("\nChecking Swagger documentation...");

  try {
    const routesPath = path.join(__dirname, "../src/routes/user.ts");
    const routesContent = fs.readFileSync(routesPath, "utf8");

    // Expected field names in Swagger docs
    const expectedFields = [
      "full_name",
      "mobile_number",
      "current_location",
      "linkedin_url",
      "company",
      "job_title",
      "course_id",
      "batch_year",
      "privacy_settings",
    ];

    // Old field names that should NOT be present in Swagger
    const oldFields = ["firstName", "lastName", "currentCompany", "currentPosition", "bio"];

    let allExpectedFound = true;
    let noOldFieldsFound = true;

    // Check for expected fields in Swagger documentation
    expectedFields.forEach((field) => {
      if (routesContent.includes(`${field}:`)) {
        console.log(`✅ Found expected field in Swagger: ${field}`);
      } else {
        console.log(`❌ Missing expected field in Swagger: ${field}`);
        allExpectedFound = false;
      }
    });

    // Check that old fields are not present in Swagger
    oldFields.forEach((field) => {
      if (routesContent.includes(`${field}:`)) {
        console.log(`❌ Found old field in Swagger: ${field}`);
        noOldFieldsFound = false;
      }
    });

    if (noOldFieldsFound) {
      console.log("✅ No old field names found in Swagger documentation");
    }

    return allExpectedFound && noOldFieldsFound;
  } catch (error) {
    console.error("❌ Error reading routes file:", error.message);
    return false;
  }
}

/**
 * Check controller implementation
 */
function checkControllerImplementation() {
  console.log("\nChecking controller implementation...");

  try {
    const controllerPath = path.join(__dirname, "../src/controllers/userController.ts");
    const controllerContent = fs.readFileSync(controllerPath, "utf8");

    // Expected field names in controller
    const expectedFields = [
      "full_name",
      "mobile_number",
      "current_location",
      "linkedin_url",
      "company",
      "job_title",
      "course_id",
      "batch_year",
      "privacy_settings",
    ];

    let allFieldsFound = true;

    // Check for expected fields in controller
    expectedFields.forEach((field) => {
      if (controllerContent.includes(field)) {
        console.log(`✅ Found field in controller: ${field}`);
      } else {
        console.log(`❌ Missing field in controller: ${field}`);
        allFieldsFound = false;
      }
    });

    return allFieldsFound;
  } catch (error) {
    console.error("❌ Error reading controller file:", error.message);
    return false;
  }
}

/**
 * Run all verification tests
 */
function runVerification() {
  console.log("Starting field verification tests...\n");

  const validationTest = checkValidationMiddleware();
  const swaggerTest = checkSwaggerDocumentation();
  const controllerTest = checkControllerImplementation();

  console.log("\n" + "=".repeat(50));
  console.log("VERIFICATION SUMMARY");
  console.log("=".repeat(50));
  console.log(`Validation Middleware: ${validationTest ? "PASS ✅" : "FAIL ❌"}`);
  console.log(`Swagger Documentation: ${swaggerTest ? "PASS ✅" : "FAIL ❌"}`);
  console.log(`Controller Implementation: ${controllerTest ? "PASS ✅" : "FAIL ❌"}`);

  if (validationTest && swaggerTest && controllerTest) {
    console.log("\n🎉 ALL TESTS PASSED! Profile update functionality is correctly configured.");
    console.log("The validation, documentation, and controller all use consistent field names.");
  } else {
    console.log("\n⚠️  Some tests failed. Please review the issues above.");
  }
}

// Run the verification
runVerification();
