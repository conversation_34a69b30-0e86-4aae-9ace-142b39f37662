import { Request, Response, NextFunction } from "express";
import { UserRole, UserStatus } from "@prisma/client";
import { prisma } from "../config/database";
import { AuthUtils } from "../utils/auth";
import { createError } from "../middleware/errorHandler";
import crypto from "crypto";
import nodemailer from "nodemailer";

interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  mobile_number?: string;
  usn?: string;
  course_name?: string;
  batch_year?: number;
  role: UserRole;
  tenant_id: number;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface RefreshTokenRequest {
  refreshToken: string;
}

interface ForgotPasswordRequest {
  email: string;
}

interface ResetPasswordRequest {
  password: string;
}

const validateRegistrationData = (role: UserRole, usn?: string, course_name?: string) => {
  const isAdmin = role === UserRole.TENANT_ADMIN || role === UserRole.SUPER_ADMIN;

  if (!isAdmin && !usn) {
    throw createError("USN is required for students and alumni", 400);
  }

  if (!isAdmin && !course_name) {
    throw createError("Course name is required for students and alumni", 400);
  }

  return isAdmin;
};

const validateTenant = async (tenant_id: number) => {
  const tenant = await prisma.tenant.findFirst({
    where: {
      id: tenant_id,
      is_active: true,
    },
  });

  if (!tenant) {
    throw createError("Invalid or inactive tenant", 400);
  }

  return tenant;
};

const checkExistingUser = async (tenant_id: number, email: string, usn?: string) => {
  const where: any = {
    tenant_id,
    OR: [{ email }],
  };

  if (usn) {
    where.OR.push({ usn });
  }

  const existingUser = await prisma.user.findFirst({
    where,
  });

  if (existingUser) {
    if (existingUser.email === email) {
      throw createError("User with this email already exists in this organization", 409);
    }
    if (usn && existingUser.usn === usn) {
      throw createError("User with this USN already exists in this organization", 409);
    }
  }
};

const findOrCreateCourse = async (tenant_id: number, course_name: string) => {
  let course = await prisma.course.findFirst({
    where: {
      tenant_id,
      course_name,
    },
  });

  if (!course) {
    course = await prisma.course.create({
      data: {
        tenant_id,
        course_name,
      },
    });
  }

  return course;
};

// Email setup - TODO: move to service layer later
const createEmailTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST || "smtp.gmail.com",
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

const generateToken = () => {
  return crypto.randomBytes(32).toString("hex");
};

// Helper to check if email service is configured
const isEmailConfigured = () => {
  return !!(process.env.SMTP_USER && process.env.SMTP_PASS);
};

export const register = async (req: Request<{}, {}, RegisterRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password, full_name, mobile_number, usn, course_name, batch_year, role, tenant_id } = req.body;

    const isAdmin = validateRegistrationData(role, usn, course_name);
    const tenant = await validateTenant(tenant_id);
    await checkExistingUser(tenant_id, email, usn);

    const hashedPassword = await AuthUtils.hashPassword(password);

    let course = null;
    if (!isAdmin && course_name) {
      course = await findOrCreateCourse(tenant_id, course_name);
    }

    const user = await prisma.user.create({
      data: {
        tenant_id,
        email,
        password_hash: hashedPassword,
        full_name,
        mobile_number: mobile_number ?? null,
        usn: usn || `ADMIN_${Date.now()}`, // Generate unique USN for admins
        role,
        account_status: isAdmin ? UserStatus.APPROVED : UserStatus.PENDING,
      },
      select: {
        id: true,
        tenant_id: true,
        email: true,
        full_name: true,
        usn: true,
        role: true,
        account_status: true,
        created_at: true,
      },
    });

    if (!isAdmin || course) {
      await prisma.userProfile.create({
        data: {
          user_id: user.id,
          tenant_id,
          course_id: course?.id || null,
          batch_year: batch_year || null,
          privacy_settings: {
            show_email: false,
            show_mobile: false,
            show_linkedin: true,
          },
        },
      });
    }

    // Send verification email
    const verificationToken = generateToken();
    await prisma.user.update({
      where: { id: user.id },
      data: {
        email_verification_token: verificationToken,
      },
    });

    if (isEmailConfigured()) {
      try {
        const transporter = createEmailTransporter();
        const verifyUrl = `${process.env.FRONTEND_URL}/verify-email/${verificationToken}`;

        await transporter.sendMail({
          from: process.env.FROM_EMAIL || "<EMAIL>",
          to: email,
          subject: "Verify Your Email Address",
          html: `
            <h2>Welcome to ${tenant.name}!</h2>
            <p>Please verify your email address by clicking the link below:</p>
            <a href="${verifyUrl}">Verify Email</a>
            <p>This link will expire in 24 hours.</p>
          `,
        });
      } catch (emailError) {
        console.error("Failed to send verification email:", emailError);
        // Don't fail registration if email fails
      }
    } else {
      console.log("Email not configured, skipping verification email");
    }

    res.status(201).json({
      message: isAdmin
        ? "Admin registration successful. Your account is approved."
        : "Registration successful. Your account is pending approval.",
      user: {
        ...user,
        course_name: course?.course_name || null,
        batch_year: batch_year || null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const login = async (req: Request<{}, {}, LoginRequest>, res: Response, next: NextFunction) => {
  try {
    const { email, password } = req.body;

    // TODO: Add rate limiting per IP
    const user = await prisma.user.findFirst({
      where: {
        email,
        tenant: {
          is_active: true,
        },
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
            is_active: true,
          },
        },
      },
    });

    if (!user) {
      throw createError("Invalid email or password", 401);
    }

    const isPasswordValid = await AuthUtils.comparePassword(password, user.password_hash);
    if (!isPasswordValid) {
      throw createError("Invalid email or password", 401);
    }

    if (user.account_status === UserStatus.REJECTED) {
      throw createError("Your account has been rejected. Please contact admin.", 403);
    }

    if (user.account_status === UserStatus.DEACTIVATED) {
      throw createError("Your account has been deactivated. Please contact admin.", 403);
    }

    if (user.account_status === UserStatus.PENDING) {
      throw createError("Your account is pending approval. Please wait for admin approval.", 403);
    }

    const tokens = AuthUtils.generateTokenPair({
      id: user.id.toString(),
      email: user.email,
      role: user.role,
      account_status: user.account_status,
      tenant_id: user.tenant_id,
    });

    res.json({
      message: "Login successful",
      accessToken: tokens.accessToken,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        account_status: user.account_status,
        tenant: user.tenant,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const logout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Extract token from request header
    const token = req.headers.authorization?.replace("Bearer ", "");

    if (token) {
      // Import the TokenBlacklistService
      const { TokenBlacklistService } = await import("../services/tokenBlacklistService");

      // Blacklist the current access token
      await TokenBlacklistService.blacklistToken(token);
    }

    // Clear refresh token cookie
    res.clearCookie("refreshToken");

    res.json({
      success: true,
      message: "Logout successful",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const refreshToken = async (req: Request<{}, {}, RefreshTokenRequest>, res: Response, next: NextFunction) => {
  try {
    const { refreshToken } = req.body;

    const token = refreshToken || req.cookies.refreshToken;

    if (!token) {
      throw createError("Refresh token is required", 401);
    }

    const payload = AuthUtils.verifyRefreshToken(token);

    const user = await prisma.user.findUnique({
      where: { id: parseInt(payload.userId) },
      include: {
        tenant: {
          select: {
            is_active: true,
          },
        },
      },
    });

    if (!user || !user.tenant.is_active) {
      throw createError("User not found or tenant inactive", 401);
    }

    if (user.account_status !== UserStatus.APPROVED) {
      throw createError("Account is not approved", 403);
    }

    const tokens = AuthUtils.generateTokenPair({
      id: user.id.toString(),
      email: user.email,
      role: user.role,
      account_status: user.account_status,
      tenant_id: user.tenant_id,
    });

    res.cookie("refreshToken", tokens.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    res.json({
      message: "Token refreshed successfully",
      accessToken: tokens.accessToken,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const getCurrentUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw createError("User not authenticated", 401);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.user.userId) },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            subdomain: true,
          },
        },
        profile: {
          include: {
            course: {
              select: {
                course_name: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw createError("User not found", 404);
    }

    res.json({
      user,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const verifyEmail = async (req: Request<{ token: string }>, res: Response, next: NextFunction) => {
  try {
    const { token } = req.params;

    if (!token) {
      throw createError("Verification token is required", 400);
    }

    const user = await prisma.user.findFirst({
      where: {
        email_verification_token: token,
      },
    });

    if (!user) {
      throw createError("Invalid or expired verification token", 400);
    }

    if (user.email_verified) {
      res.json({
        message: "Email is already verified.",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        email_verified: true,
        email_verification_token: null,
      },
    });

    res.json({
      message: "Email verification successful! Your account is now verified.",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const forgotPassword = async (
  req: Request<{}, {}, ForgotPasswordRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email } = req.body;

    const user = await prisma.user.findFirst({
      where: {
        email,
        tenant: {
          is_active: true,
        },
      },
      include: {
        tenant: true,
      },
    });

    // Check if user recently requested a reset (simple rate limiting)
    if (user && user.reset_expires && user.reset_expires > new Date()) {
      res.json({
        message: "If an account with that email exists, we've sent a password reset link.",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    // Always return success to prevent email enumeration
    if (!user) {
      res.json({
        message: "If an account with that email exists, we've sent a password reset link.",
        timestamp: new Date().toISOString(),
      });
      return;
    }

    const resetToken = generateToken();
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    await prisma.user.update({
      where: { id: user.id },
      data: {
        reset_token: resetToken,
        reset_expires: resetExpires,
      },
    });

    // Send email if configured
    if (isEmailConfigured()) {
      try {
        const transporter = createEmailTransporter();
        const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;

        await transporter.sendMail({
          from: process.env.FROM_EMAIL || "<EMAIL>",
          to: email,
          subject: "Password Reset Request",
          html: `
            <h2>Password Reset Request</h2>
            <p>You requested a password reset for your ${user.tenant.name} account.</p>
            <p>Click the link below to reset your password:</p>
            <a href="${resetUrl}">Reset Password</a>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this, please ignore this email.</p>
          `,
        });
      } catch (emailError) {
        console.error("Failed to send reset email:", emailError);
        // Don't expose email errors to user
      }
    }

    res.json({
      message: "If an account with that email exists, we've sent a password reset link.",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    next(error);
  }
};

export const resetPassword = async (
  req: Request<{ token: string }, {}, ResetPasswordRequest>,
  res: Response,
  next: NextFunction
) => {
  try {
    const { token } = req.params;
    const { password } = req.body;

    // Validate password strength
    const passwordValidation = AuthUtils.validatePassword(password);
    if (!passwordValidation.isValid) {
      throw createError(passwordValidation.errors.join(", "), 400);
    }

    const user = await prisma.user.findFirst({
      where: {
        reset_token: token,
        reset_expires: {
          gt: new Date(),
        },
      },
    });

    if (!user) {
      throw createError("Invalid or expired reset token", 400);
    }

    const hashedPassword = await AuthUtils.hashPassword(password);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password_hash: hashedPassword,
        reset_token: null,
        reset_expires: null,
      },
    });

    res.json({
      message: "Password reset successful. You can now login with your new password.",
      timestamp: new Date().toISOString(),
    });

    // This would be the real implementation:
    // const hashedPassword = await AuthUtils.hashPassword(password);
    //
    // await prisma.user.update({
    //   where: { id: user.id },
    //   data: {
    //     password_hash: hashedPassword,
    //     reset_token: null,
    //     reset_expires: null,
    //   },
    // });
    //
    // res.json({
    //   message: "Password reset successful. You can now login with your new password.",
    //   timestamp: new Date().toISOString(),
    // });
  } catch (error) {
    next(error);
  }
};
