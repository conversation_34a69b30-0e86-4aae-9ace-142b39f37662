import winston from "winston";
import path from "path";

const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const logColors = {
  error: "red",
  warn: "yellow",
  info: "green",
  http: "magenta",
  debug: "white",
};

winston.addColors(logColors);

const logsDir = path.join(process.cwd(), "logs");

const logFormat = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss:ms" }),
  winston.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`)
);

const transports = [];

if (process.env.NODE_ENV === "development") {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: "debug",
    })
  );
} else {
  transports.push(
    new winston.transports.Console({
      format: logFormat,
      level: "info",
    })
  );
}

if (process.env.NODE_ENV === "production") {
  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, "error.log"),
      level: "error",
      format: logFormat,
      maxsize: 5242880,
      maxFiles: 5,
    })
  );

  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, "combined.log"),
      format: logFormat,
      maxsize: 5242880,
      maxFiles: 5,
    })
  );

  transports.push(
    new winston.transports.File({
      filename: path.join(logsDir, "access.log"),
      level: "http",
      format: logFormat,
      maxsize: 5242880,
      maxFiles: 5,
    })
  );
}

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === "development" ? "debug" : "info"),
  levels: logLevels,
  format: logFormat,
  transports,
  exitOnError: false,
});

export class Logger {
  static error(message: string, meta?: any) {
    logger.error(message, meta);
  }

  static warn(message: string, meta?: any) {
    logger.warn(message, meta);
  }

  static info(message: string, meta?: any) {
    logger.info(message, meta);
  }

  static http(message: string, meta?: any) {
    logger.http(message, meta);
  }

  static debug(message: string, meta?: any) {
    logger.debug(message, meta);
  }

  static auth(message: string, userId?: string, meta?: any) {
    logger.info(`[AUTH] ${message}`, { userId, ...meta });
  }

  static security(message: string, ip?: string, meta?: any) {
    logger.warn(`[SECURITY] ${message}`, { ip, ...meta });
  }

  static performance(message: string, duration?: number, meta?: any) {
    logger.info(`[PERFORMANCE] ${message}`, { duration, ...meta });
  }

  // Request logging middleware
  static requestLogger() {
    return (req: any, res: any, next: any) => {
      const start = Date.now();

      res.on("finish", () => {
        const duration = Date.now() - start;
        const { method, url, ip } = req;
        const { statusCode } = res;

        const message = `${method} ${url} ${statusCode} - ${duration}ms`;

        if (statusCode >= 400) {
          Logger.error(message, {
            method,
            url,
            statusCode,
            duration,
            ip,
            userAgent: req.get("User-Agent"),
            userId: req.user?.userId,
          });
        } else {
          Logger.http(message, {
            method,
            url,
            statusCode,
            duration,
            ip,
            userId: req.user?.userId,
          });
        }
      });

      next();
    };
  }

  // Error logging middleware
  static errorLogger() {
    return (err: any, req: any, res: any, next: any) => {
      const { method, url, ip } = req;

      Logger.error(`Unhandled error: ${err.message}`, {
        error: err.stack,
        method,
        url,
        ip,
        userAgent: req.get("User-Agent"),
        userId: req.user?.userId,
        body: req.body,
        params: req.params,
        query: req.query,
      });

      next(err);
    };
  }

  static async measurePerformance<T>(operation: string, fn: () => Promise<T>): Promise<T> {
    const start = Date.now();

    try {
      const result = await fn();
      const duration = Date.now() - start;

      if (duration > 1000) {
        Logger.warn(`Slow operation: ${operation} (${duration}ms)`);
      }

      return result;
    } catch (error: any) {
      const duration = Date.now() - start;
      Logger.error(`Operation failed: ${operation} (${duration}ms)`, error);
      throw error;
    }
  }
}

export const morganStream = {
  write: (message: string) => {
    Logger.http(message.trim());
  },
};

export { logger };
export default Logger;
